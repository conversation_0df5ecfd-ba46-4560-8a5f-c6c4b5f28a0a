import { spfi, <PERSON>FI } from "@pnp/sp";
import { SPFx } from "@pnp/sp";
import "@pnp/sp/webs";
import "@pnp/sp/lists";
import "@pnp/sp/items";
import type { BaseComponentContext } from "@microsoft/sp-component-base"; // ⬅️ add this

let _sp: SPFI | undefined = undefined;

export const getSP = (context?: BaseComponentContext): SPFI => { // ⬅️ typed
  if (!_sp && context) {
    _sp = spfi().using(SPFx(context));
  }
  if (!_sp) {
    throw new Error("PnP SP not initialized. Call getSP(this.context) in onInit().");
  }
  return _sp;
};