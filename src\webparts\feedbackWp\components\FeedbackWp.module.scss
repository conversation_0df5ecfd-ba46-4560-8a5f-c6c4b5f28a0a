.blockTrainingFeedback {
  .container {
    max-width: 720px;
    margin: 0 auto;
  }

  .header {
    background: #323130;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 16px;
    box-shadow: 0 1px 2px rgba(16,24,40,0.04);
    text-align: center;
  }

  .title {
    font-weight: 700;
    color: #ffd700;
  }

  .subtitle {
    color: #6b7280;
    margin-top: 4px;
  }

  .messageBar {
    margin: 12px 0;
  }

  .formContainer {
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 0 6px #c8c6c4;
  }

  .ratingSection {
    .ratingLabel {
      font-weight: 600;
    }
    .required {
      color: #dc2626;
    }
    .ratingContainer {
      background: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      padding: 12px;
      margin-top: 6px;
    }
    .ratingText {
      color: #6b7280;
      margin-left: 8px;
    }
    .errorText {
      color: #dc2626;
      margin-top: 6px;
    }
  }

  .buttonContainer{
    display: flex;
    justify-content: flex-end;
  }

  .submitButton {
    width: 30%;
    margin-top: 8px;
    background-color: #323130 !important;
    color:#ffd700 !important;
    border-radius: 50px;
  }
  .submitButton:hover{
    background-color: #ffd700 !important;
    color: #323130 !important;
  }
}
