Stack trace:
Frame         Function      Args
0007FFFFBF80  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFBF80, 0007FFFFAE80) msys-2.0.dll+0x2118E
0007FFFFBF80  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x69BA
0007FFFFBF80  0002100469F2 (00021028DF99, 0007FFFFBE38, 0007FFFFBF80, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBF80  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBF80  00021006A545 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFC260  00021006B9A5 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF991580000 ntdll.dll
7FF990710000 KERNEL32.DLL
7FF98E930000 KERNELBASE.dll
7FF98E3C0000 hmpalert.dll
7FF98E200000 SophosED.dll
7FF991160000 USER32.dll
7FF98F0B0000 win32u.dll
7FF98F4C0000 GDI32.dll
7FF98EF70000 gdi32full.dll
7FF98F260000 msvcp_win.dll
7FF98E740000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF9904D0000 advapi32.dll
7FF98FBF0000 msvcrt.dll
7FF98FB40000 sechost.dll
7FF990590000 RPCRT4.dll
7FF98D830000 CRYPTBASE.DLL
7FF98E890000 bcryptPrimitives.dll
7FF98F4F0000 IMM32.DLL
