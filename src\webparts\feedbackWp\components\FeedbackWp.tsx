import * as React from "react";
import { useEffect, useMemo, useState } from "react";
import type { IFeedbackWpProps } from "./IFeedbackWpProps";
import { getSP } from "./pnpConfig";
import type { SPFI } from "@pnp/sp";

import "@pnp/sp/site-users/web"; // enables web.currentUser()
import "@pnp/sp/webs";

import {
  Stack,
  Text,
  PrimaryButton,
  DefaultButton,
  MessageBar,
  MessageBarType,
  TextField,
  Dropdown,
  IDropdownOption,
  ChoiceGroup,
  IChoiceGroupOption,
} from "@fluentui/react";

// Ensure Fluent UI icon font is registered (chevrons, etc.)
import { initializeIcons } from "@fluentui/font-icons-mdl2";
initializeIcons();

// Star icons
import { IoIosStar } from "react-icons/fa";

/* ---------------- StarRating (react-icons) ---------------- */
type StarRatingProps = {
  max?: number;
  value: number;
  onChange?: (n: number) => void;
  size?: number;
  readOnly?: boolean;
  title?: string;
};

function StarRating({
  max = 5,
  value,
  onChange,
  size = 22,
  readOnly = false,
  title,
}: StarRatingProps) {
  const clickable = !!onChange && !readOnly;

  return (
    <div
      role="slider"
      aria-valuemin={0}
      aria-valuemax={max}
      aria-valuenow={value}
      aria-label={title || "Rating"}
      tabIndex={clickable ? 0 : -1}
      onKeyDown={(e) => {
        if (!clickable) return;
        if (e.key === "ArrowRight" || e.key === "ArrowUp") {
          e.preventDefault();
          onChange?.(Math.min(max, (value || 0) + 1));
        } else if (e.key === "ArrowLeft" || e.key === "ArrowDown") {
          e.preventDefault();
          onChange?.(Math.max(0, (value || 0) - 1));
        } else if (e.key >= "1" && e.key <= String(max)) {
          onChange?.(parseInt(e.key, 10));
        }
      }}
      style={{ display: "flex", gap: 6, alignItems: "center" }}
      title={title}
    >
      {Array.from({ length: max }, (_, i) => {
        const starValue = i + 1;
        const filled = starValue <= value;
        return (
          <IoIosStar
            key={i}
            size={size}
            color={filled ? "#FFD54F" : "#D0D7DE"}
            style={{
              cursor: clickable ? "pointer" : "default",
              transition: "transform 120ms ease",
            }}
            onClick={() => clickable && onChange?.(starValue)}
            onMouseEnter={(e) => {
              if (!clickable) return;
              (e.currentTarget as HTMLElement).style.transform = "scale(1.05)";
            }}
            onMouseLeave={(e) => {
              if (!clickable) return;
              (e.currentTarget as HTMLElement).style.transform = "scale(1)";
            }}
            aria-label={`${starValue} star${starValue > 1 ? "s" : ""}`}
          />
        );
      })}
    </div>
  );
}

/* ---------------- Lists ---------------- */
const LIST_FEEDBACK_INVITES = "FeedbackInvites";
const LIST_FEEDBACK_RESPONSES = "FeedbackResponses";
const LIST_TRAINING_BOOKING = "TrainingBookings";

/* ---------------- Types ---------------- */
type View = "loading" | "invalid" | "forbidden" | "form" | "thanks" | "already";
type YesNo = "Yes" | "No";

/* ---------------- Options ---------------- */
const yesNoOptions: IChoiceGroupOption[] = [
  { key: "Yes", text: "Yes" },
  { key: "No", text: "No" },
];

const depthOptions: IDropdownOption[] = [
  { key: "Too basic", text: "Too basic" },
  { key: "Just right", text: "Just right" },
  { key: "Too advanced", text: "Too advanced" },
];

const paceOptions: IDropdownOption[] = [
  { key: "Too slow", text: "Too slow" },
  { key: "Just right", text: "Just right" },
  { key: "Too fast", text: "Too fast" },
];

const durationOptions: IDropdownOption[] = [
  { key: "Too short", text: "Too short" },
  { key: "Just right", text: "Just right" },
  { key: "Too long", text: "Too long" },
];

const materialsOptions: IDropdownOption[] = [
  { key: "Agree", text: "Agree" },
  { key: "Neutral", text: "Neutral" },
  { key: "Disagree", text: "Disagree" },
];

/* ---------------- Utils ---------------- */
function toMessage(e: unknown): string {
  return e instanceof Error ? e.message : String(e);
}

/* ---------------- Component ---------------- */
export default function FeedbackWp(
  props: IFeedbackWpProps
): React.ReactElement {
  const sp: SPFI = getSP(); // PnP already initialized in onInit()

  const [view, setView] = useState<View>("loading");
  const [error, setError] = useState<string>("");

  const [inviteId, setInviteId] = useState<number | null>(null);
  const [bookingId, setBookingId] = useState<number | null>(null);
  const [inviteEmail, setInviteEmail] = useState<string>("");
  const [, setResponseId] = useState<number | null>(null);

  // Header (from TrainingBookings)
  const [courseTitle, setCourseTitle] = useState<string>("");
  const [trainingDate, setTrainingDate] = useState<string>("");
  const [trainerName, setTrainerName] = useState<string>("");
  const [sessionChoice, setSessionChoice] = useState<string>("");

  // Top-level fields in FeedbackResponses
  const [rating, setRating] = useState<number>(0);
  const [comments, setComments] = useState<string>("");

  // Extended Qs (go to AnswersJson)
  const [contentRelevant, setContentRelevant] = useState<YesNo | "">("");
  const [objectivesClear, setObjectivesClear] = useState<number>(0);
  const [contentDepth, setContentDepth] = useState<string>("");

  const [trainerClarity, setTrainerClarity] = useState<number>(0);
  const [trainerKnowledge, setTrainerKnowledge] = useState<number>(0);
  const [participation, setParticipation] = useState<YesNo | "">("");

  const [pace, setPace] = useState<string>("");
  const [duration, setDuration] = useState<string>("");
  const [materialsUseful, setMaterialsUseful] = useState<string>("");

  const [applyConfidence, setApplyConfidence] = useState<number>(0);
  const [skillsImmediate, setSkillsImmediate] = useState<string>("");
  const [needsFocus, setNeedsFocus] = useState<string>("");
  const [overallSatisfaction, setOverallSatisfaction] = useState<number>(0);
  const [recommend, setRecommend] = useState<YesNo | "">("");

  const token = useMemo<string | null>(() => {
    try {
      return new URL(window.location.href).searchParams.get("token");
    } catch {
      return null;
    }
  }, []);

  useEffect((): void => {
    const run = async (): Promise<void> => {
      try {
        if (!token) {
          setView("invalid");
          return;
        }

        // 1) Invite by token
        const invites = await sp.web.lists
          .getByTitle(LIST_FEEDBACK_INVITES)
          .items.select("Id,Token,BookingId,TraineeEmail,Used,SubmittedItemId")
          .filter(`Token eq '${token.replace(/'/g, "''")}'`)
          .top(1)();

        const invite = invites?.[0];
        if (!invite) {
          setView("invalid");
          return;
        }

        // 2) Current user must match invite.TraineeEmail
        const me = await sp.web.currentUser();
        const meEmail = (me.Email || "").toLowerCase();
        const invitedEmail = (invite.TraineeEmail || "").toLowerCase();
        if (!meEmail || meEmail !== invitedEmail) {
          setView("forbidden");
          return;
        }

        setInviteId(invite.Id);
        setInviteEmail(invite.TraineeEmail);
        setBookingId(invite.BookingId);

        // 3) Load TrainingBookings header
        if (invite.BookingId) {
          const booking = await sp.web.lists
            .getByTitle(LIST_TRAINING_BOOKING)
            .items.getById(invite.BookingId)
            .select("Title,TrainingDate,Session,Trainer/Title")
            .expand("Trainer")();

          setCourseTitle(booking?.Title || "");
          setTrainingDate(booking?.TrainingDate || "");
          setSessionChoice(booking?.Session || "");
          setTrainerName(booking?.Trainer?.Title || "");
        }

        // 4) Already submitted?
        if (invite.Used || invite.SubmittedItemId) {
          setResponseId(invite.SubmittedItemId || null);
          setView("already");
          return;
        }

        // 5) Race check: existing response?
        const existing = await sp.web.lists
          .getByTitle(LIST_FEEDBACK_RESPONSES)
          .items.select("Id")
          .filter(
            `BookingId eq ${
              invite.BookingId
            } and TraineeEmail eq '${invite.TraineeEmail.replace(/'/g, "''")}'`
          )
          .top(1)();

        if (existing?.[0]?.Id) {
          setResponseId(existing[0].Id);
          await sp.web.lists
            .getByTitle(LIST_FEEDBACK_INVITES)
            .items.getById(invite.Id)
            .update({ Used: true, SubmittedItemId: existing[0].Id });
          setView("already");
          return;
        }

        setView("form");
      } catch (e) {
        setError(e instanceof Error ? e.message : String(e));
        setView("invalid");
      }
    };

    run().catch((e) => {
      setError(e instanceof Error ? e.message : String(e));
      setView("invalid");
    });
  }, [token, sp]);

  async function handleSubmit(): Promise<void> {
    try {
      if (!inviteId || !bookingId || !inviteEmail) return;

      const answers = {
        ContentRelevance: contentRelevant || null,
        ObjectivesClear: objectivesClear,
        ContentDepth: contentDepth || null,
        TrainerClarity: trainerClarity,
        TrainerKnowledge: trainerKnowledge,
        TrainerEncouragedParticipation: participation || null,
        Pace: pace || null,
        Duration: duration || null,
        MaterialsUseful: materialsUseful || null,
        ConfidenceToApply: applyConfidence,
        SkillsToUseImmediately: skillsImmediate || "",
        TopicsNeedingMoreFocus: needsFocus || "",
        OverallSatisfaction: overallSatisfaction,
        Recommend: recommend || null,
        CourseTitle: courseTitle || "",
        TrainingDate: trainingDate || "",
        TrainerName: trainerName || "",
        Session: sessionChoice || "",
      };

      const add = await sp.web.lists
        .getByTitle(LIST_FEEDBACK_RESPONSES)
        .items.add({
          Title: `Response - ${courseTitle || "Training"} - ${inviteEmail}`,
          BookingId: bookingId,
          TraineeEmail: inviteEmail,
          Rating: rating,
          Comments: comments,
          AnswersJson: JSON.stringify(answers),
        });

      await sp.web.lists
        .getByTitle(LIST_FEEDBACK_INVITES)
        .items.getById(inviteId)
        .update({ Used: true, SubmittedItemId: add.data.Id });

      setResponseId(add.data.Id);
      setView("thanks");
    } catch (e: unknown) {
      setError(toMessage(e));
    }
  }

  /* ---------------- UI Shell ---------------- */
  const Shell = (children: React.ReactNode): JSX.Element => (
    <div style={{ maxWidth: 760, margin: "0 auto", padding: 16 }}>
      <div
        style={{
          background: "#fff",
          border: "1px solid #e5e5e5",
          borderRadius: 12,
          padding: 20,
          boxShadow: "0 4px 14px rgba(0,0,0,0.06)",
        }}
      >
        <Header
          title={courseTitle || "Training Feedback"}
          meta={[
            trainingDate ? new Date(trainingDate).toLocaleString() : "",
            sessionChoice ? `Session: ${sessionChoice}` : "",
            trainerName ? `Trainer: ${trainerName}` : "",
          ]
            .filter(Boolean)
            .join(" • ")}
        />
        {children}
      </div>
    </div>
  );

  if (view === "loading") return Shell(<Text>Loading…</Text>);
  if (view === "invalid")
    return Shell(
      <MessageBar messageBarType={MessageBarType.error}>
        Invalid or missing link. {error}
      </MessageBar>
    );
  if (view === "forbidden")
    return Shell(
      <MessageBar messageBarType={MessageBarType.severeWarning}>
        This link isn’t for your account.
      </MessageBar>
    );

  if (view === "form") {
    return Shell(
      <Stack tokens={{ childrenGap: 18 }}>
        {/* Content */}
        <Text variant="large" styles={{ root: { fontWeight: 600 } }}>
          Training Content
        </Text>
        <ChoiceGroup
          label="Was the content relevant to your work?"
          options={yesNoOptions}
          selectedKey={contentRelevant || undefined}
          onChange={(_, opt) => setContentRelevant((opt?.key as YesNo) ?? "")}
        />
        <div>
          <Text>The training objectives were clearly defined.</Text>
          <StarRating
            title="Objectives clarity"
            value={objectivesClear}
            onChange={(n) => setObjectivesClear(n)}
          />
        </div>
        <Dropdown
          label="Content depth"
          options={depthOptions}
          selectedKey={contentDepth || undefined}
          onChange={(_, opt) => setContentDepth((opt?.key as string) ?? "")}
        />

        {/* Trainer */}
        <Text
          variant="large"
          styles={{ root: { fontWeight: 600, marginTop: 8 } }}
        >
          Trainer & Delivery
        </Text>
        <div>
          <Text>The trainer explained concepts clearly and effectively.</Text>
          <StarRating
            title="Trainer clarity"
            value={trainerClarity}
            onChange={(n) => setTrainerClarity(n)}
          />
        </div>
        <div>
          <Text>The trainer was knowledgeable about the subject.</Text>
          <StarRating
            title="Trainer knowledge"
            value={trainerKnowledge}
            onChange={(n) => setTrainerKnowledge(n)}
          />
        </div>
        <ChoiceGroup
          label="The trainer encouraged questions and participation."
          options={yesNoOptions}
          selectedKey={participation || undefined}
          onChange={(_, opt) => setParticipation((opt?.key as YesNo) ?? "")}
        />

        {/* Experience */}
        <Text
          variant="large"
          styles={{ root: { fontWeight: 600, marginTop: 8 } }}
        >
          Training Experience
        </Text>
        <Dropdown
          label="Pace of the training"
          options={paceOptions}
          selectedKey={pace || undefined}
          onChange={(_, opt) => setPace((opt?.key as string) ?? "")}
        />
        <Dropdown
          label="Duration of the session"
          options={durationOptions}
          selectedKey={duration || undefined}
          onChange={(_, opt) => setDuration((opt?.key as string) ?? "")}
        />
        <Dropdown
          label="Training materials/slides were useful"
          options={materialsOptions}
          selectedKey={materialsUseful || undefined}
          onChange={(_, opt) => setMaterialsUseful((opt?.key as string) ?? "")}
        />

        {/* Practical Application */}
        <Text
          variant="large"
          styles={{ root: { fontWeight: 600, marginTop: 8 } }}
        >
          Practical Application
        </Text>
        <div>
          <Text>I feel confident to apply what I learned.</Text>
          <StarRating
            title="Confidence to apply"
            value={applyConfidence}
            onChange={(n) => setApplyConfidence(n)}
          />
        </div>
        <TextField
          label="Which skills or knowledge will you use immediately?"
          multiline
          rows={4}
          value={skillsImmediate}
          onChange={(_, v) => setSkillsImmediate(v ?? "")}
        />
        <TextField
          label="What topics need more focus or deeper explanation?"
          multiline
          rows={4}
          value={needsFocus}
          onChange={(_, v) => setNeedsFocus(v ?? "")}
        />

        {/* Overall */}
        <Text
          variant="large"
          styles={{ root: { fontWeight: 600, marginTop: 8 } }}
        >
          Overall
        </Text>
        <div>
          <Text>Overall satisfaction with this training</Text>
          <StarRating
            title="Overall satisfaction"
            value={overallSatisfaction}
            onChange={(n) => setOverallSatisfaction(n)}
          />
        </div>
        <ChoiceGroup
          label="Would you recommend this training to colleagues?"
          options={yesNoOptions}
          selectedKey={recommend || undefined}
          onChange={(_, opt) => setRecommend((opt?.key as YesNo) ?? "")}
        />
        <TextField
          label="Additional comments (optional)"
          multiline
          rows={5}
          value={comments}
          onChange={(_, v) => setComments(v ?? "")}
        />

        {/* Quick overall star (top-level Rating column) */}
        <div>
          <Text>Quick overall rating</Text>
          <StarRating
            title="Quick overall rating"
            value={rating}
            onChange={(n) => setRating(n)}
          />
        </div>

        {error && (
          <MessageBar messageBarType={MessageBarType.error}>{error}</MessageBar>
        )}

        <Stack horizontal tokens={{ childrenGap: 8 }}>
          <PrimaryButton text="Submit" onClick={handleSubmit} />
          <DefaultButton
            text="Back to portal"
            onClick={(): void => {
              window.location.href = "/sites/<site>";
            }}
          />
        </Stack>
      </Stack>
    );
  }

  if (view === "thanks") {
    return Shell(
      <MessageBar messageBarType={MessageBarType.success} isMultiline>
        Thanks! Your feedback has been submitted. You can close this page now.
      </MessageBar>
    );
  }

  // Already submitted
  return Shell(
    <Stack tokens={{ childrenGap: 12 }}>
      <MessageBar messageBarType={MessageBarType.success}>
        Thanks! You’ve already submitted this feedback for this booking.
      </MessageBar>
      <DefaultButton
        text="Back to portal"
        onClick={(): void => {
          window.location.href = "/sites/<site>";
        }}
      />
    </Stack>
  );
}

/* ---------------- Header ---------------- */
function Header({
  title,
  meta,
}: {
  title: string;
  meta?: string;
}): JSX.Element {
  return (
    <Stack tokens={{ childrenGap: 4 }} styles={{ root: { marginBottom: 12 } }}>
      <Text variant="xLargePlus" styles={{ root: { fontWeight: 600 } }}>
        {title}
      </Text>
      {meta && (
        <Text variant="smallPlus" styles={{ root: { color: "#666" } }}>
          {meta}
        </Text>
      )}
    </Stack>
  );
}
